"""
Cancer Term Standardization Module

This module provides cancer term standardization functionality using OpenAI embeddings
and MONDO ontology. It processes extraction pipeline results to standardize cancer_type
and cancer_subtype fields.

Usage:
    python -m src.cancer_term_standardizer --input-dir results/ --output-dir standardized_results/
"""

import os
import json
import pickle
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union, Any
from dotenv import load_dotenv
import numpy as np
import torch
import ssl
import urllib3
import requests

# Comprehensive SSL bypass configuration
ssl._create_default_https_context = ssl._create_unverified_context

# Disable SSL warnings
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Set environment variables to disable SSL verification
os.environ['CURL_CA_BUNDLE'] = ''
os.environ['REQUESTS_CA_BUNDLE'] = ''
os.environ['SSL_VERIFY'] = 'false'
os.environ['PYTHONHTTPSVERIFY'] = '0'

# Monkey patch requests to disable SSL verification
original_request = requests.Session.request
def patched_request(self, method, url, **kwargs):
    kwargs['verify'] = False
    return original_request(self, method, url, **kwargs)
requests.Session.request = patched_request

from transformers import AutoTokenizer, AutoModel
from oaklib import get_adapter
from oaklib.datamodels.vocabulary import IS_A
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class CancerTermStandardizer:
    """
    Cancer term standardizer using OpenAI embeddings and MONDO ontology.
    """

    def __init__(self):
        """Initialize the standardizer with sentence transformers."""
        # Additional SSL configuration
        self._configure_ssl()

        # Initialize sentence transformer model
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model_name = "sentence-transformers/all-MiniLM-L6-v2"

        try:
            # Try to load from cache first
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, local_files_only=True)
            self.model = AutoModel.from_pretrained(self.model_name, local_files_only=True).to(self.device)
            logger.info("Loaded model from local cache")
        except Exception as e:
            logger.warning(f"Could not load from cache: {e}")
            logger.info("Downloading model from Hugging Face with SSL verification disabled...")

            # Download with SSL verification disabled
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_name, trust_remote_code=True)
            self.model = AutoModel.from_pretrained(self.model_name, trust_remote_code=True).to(self.device)
            logger.info("Downloaded model from Hugging Face")

        logger.info(f"Using device: {self.device}")

        # File paths for persistent storage
        self.cancer_terms_file = 'cancer_terms.pkl'
        self.cancer_embeddings_file = 'cancer_embeddings.pkl'
        self.cancer_synonyms_file = 'cancer_synonyms.json'

        # MONDO cancer ID
        self.cancer_mondo_id = 'MONDO:0004992'

        # Number of matching standard terms to return
        self.num_output = 10

        # Templates
        self.query_template = "{term} appears in our document in the following context: {context}"
        self.chunk_template = "{term} is defined as {definition}"

        # Initialize cancer terms if not already cached
        self._ensure_cancer_terms_loaded()

    def _configure_ssl(self):
        """Configure SSL settings to bypass certificate verification."""
        import ssl
        import urllib3

        # Create unverified SSL context
        ssl._create_default_https_context = ssl._create_unverified_context

        # Disable SSL warnings
        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

        # Set environment variables
        os.environ['CURL_CA_BUNDLE'] = ''
        os.environ['REQUESTS_CA_BUNDLE'] = ''
        os.environ['SSL_VERIFY'] = 'false'
        os.environ['PYTHONHTTPSVERIFY'] = '0'

        logger.info("SSL verification disabled for external API calls")

    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """Get embeddings for a list of texts using sentence transformers."""
        embeddings = []
        batch_size = 8

        for batch_i in range(0, len(texts), batch_size):
            batch = texts[batch_i:batch_i + batch_size]
            tokens = self.tokenizer(
                batch,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=512
            ).to(self.device)

            with torch.no_grad():
                outputs = self.model(**tokens).last_hidden_state
                # Use mean pooling
                batch_embeddings = outputs.mean(dim=1)
                embeddings.extend(batch_embeddings.cpu().numpy())

        return np.array(embeddings)

    def _get_mondo_descendants(self, curie: str, adapter) -> List[Tuple[str, str]]:
        """Get all descendant cancer terms from MONDO ontology."""
        all_descendants_data = [
            (adapter.label(child), child)
            for child in adapter.descendants([curie], predicates=[IS_A])
            if child != curie and adapter.label(child) is not None
        ]
        return all_descendants_data

    def _save_synonyms(self, cancers: List[Tuple[str, str]], adapter):
        """Save cancer term synonyms to JSON file."""
        synonyms_dict = {}
        for term, curie in cancers:
            synonyms = adapter.entity_metadata_map(curie).get('oio:hasExactSynonym', [])
            if synonyms is None:
                synonyms = []
            synonyms.append(term)

            for synonym in synonyms:
                if synonym not in synonyms_dict:
                    synonyms_dict[synonym] = term

        with open(self.cancer_synonyms_file, 'w') as f:
            json.dump(synonyms_dict, f, indent=2)

        logger.info(f"Saved {len(synonyms_dict)} cancer synonyms")

    def _get_docs_and_terms(self, cancers: List[Tuple[str, str]], adapter) -> Tuple[List[str], List[str]]:
        """Generate documents and terms for embedding."""
        documents = []
        terms = []

        for term, curie in cancers:
            terms.append(term)
            definition = adapter.definition(curie)
            if definition:
                document = self.chunk_template.format(term=term, definition=definition)
            else:
                document = f"{term} is a type of cancer"
            documents.append(document)

        return documents, terms

    def _fetch_and_save_cancer_terms(self):
        """Fetch cancer terms from MONDO and save embeddings."""
        logger.info("Fetching cancer terms from MONDO ontology...")

        # Initialize MONDO adapter
        adapter = get_adapter("sqlite:obo:mondo")

        # Get cancer descendants
        cancers = self._get_mondo_descendants(self.cancer_mondo_id, adapter)
        logger.info(f"Found {len(cancers)} cancer terms")

        # Save synonyms
        self._save_synonyms(cancers, adapter)

        # Generate documents and terms
        documents, terms = self._get_docs_and_terms(cancers, adapter)

        # Get embeddings
        logger.info("Generating embeddings...")
        embeddings = self._get_embeddings(documents)

        # Save terms and embeddings
        with open(self.cancer_terms_file, 'wb') as f:
            pickle.dump(terms, f)

        with open(self.cancer_embeddings_file, 'wb') as f:
            pickle.dump(embeddings, f)

        logger.info("Cancer terms and embeddings saved successfully")

    def _ensure_cancer_terms_loaded(self):
        """Ensure cancer terms and embeddings are loaded."""
        files_exist = all(
            os.path.exists(f) for f in [
                self.cancer_terms_file,
                self.cancer_embeddings_file,
                self.cancer_synonyms_file
            ]
        )

        if not files_exist:
            logger.info("Cancer terms not found, fetching from MONDO...")
            self._fetch_and_save_cancer_terms()

    def standardize_term(self, term: str, context: Optional[str] = None) -> List[str]:
        """
        Standardize a cancer term using similarity search.

        Args:
            term: The cancer term to standardize
            context: Optional context for the term

        Returns:
            List of standardized cancer terms ranked by similarity
        """
        # Load synonyms
        with open(self.cancer_synonyms_file, 'r') as f:
            synonyms_dict = json.load(f)

        # Check for exact synonym match first
        if term in synonyms_dict:
            return [synonyms_dict[term]]

        # Load terms and embeddings
        with open(self.cancer_terms_file, 'rb') as f:
            terms = pickle.load(f)

        with open(self.cancer_embeddings_file, 'rb') as f:
            embeddings = pickle.load(f)

        # Create query
        if context:
            query = self.query_template.format(term=term, context=context)
        else:
            query = term

        # Get query embedding
        query_embedding = self._get_embeddings([query])[0]

        # Calculate similarities
        similarities = np.dot(embeddings, query_embedding) / (
            np.linalg.norm(embeddings, axis=1) * np.linalg.norm(query_embedding)
        )

        # Get top matches
        top_indices = np.argsort(similarities)[::-1][:self.num_output]
        results = [terms[i] for i in top_indices]

        return results

    def standardize_cancer_terms(self, cancer_type: str, cancer_type_context: Optional[str] = None,
                                cancer_subtype: Optional[str] = None, cancer_subtype_context: Optional[str] = None) -> Dict[str, List[str]]:
        """
        Standardize cancer type and subtype.

        Args:
            cancer_type: The cancer type to standardize
            cancer_type_context: Optional context for cancer type
            cancer_subtype: Optional cancer subtype to standardize
            cancer_subtype_context: Optional context for cancer subtype

        Returns:
            Dictionary with standardized terms
        """
        result = {
            'standardized_cancer_type': self.standardize_term(cancer_type, cancer_type_context)
        }

        if cancer_subtype:
            result['standardized_cancer_subtype'] = self.standardize_term(cancer_subtype, cancer_subtype_context)

        return result

    def process_extraction_file(self, input_file: Path, output_file: Path):
        """
        Process a single extraction JSON file to add standardized cancer terms.

        Args:
            input_file: Path to input JSON file
            output_file: Path to output JSON file with standardized terms
        """
        logger.info(f"Processing {input_file.name}")

        # Load extraction results
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # Process each experiment result
        if isinstance(data, dict) and 'experiment_results' in data:
            # New format with experiment_results
            for exp_result in data['experiment_results']:
                if 'model' in exp_result and exp_result['model']:
                    self._add_standardized_terms_to_model(exp_result['model'])
        elif isinstance(data, list):
            # Direct list of models/results
            for item in data:
                if 'cancer_type' in item:
                    self._add_standardized_terms_to_model(item)

        # Save standardized results
        output_file.parent.mkdir(parents=True, exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

        logger.info(f"Saved standardized results to {output_file}")

    def _add_standardized_terms_to_model(self, model_data: Dict[str, Any]):
        """Add standardized cancer terms to a model data dictionary."""
        cancer_type = model_data.get('cancer_type')
        cancer_subtype = model_data.get('cancer_subtype')

        if cancer_type and cancer_type.lower() != 'not cancer':
            # Get context from citations if available
            context = None
            if 'citations' in model_data and model_data['citations']:
                context = ' '.join(model_data['citations'][:2])  # Use first 2 citations as context

            # Standardize terms
            standardized = self.standardize_cancer_terms(
                cancer_type=cancer_type,
                cancer_type_context=context,
                cancer_subtype=cancer_subtype,
                cancer_subtype_context=context
            )

            # Add standardized fields
            model_data['standardized_cancer_type'] = standardized['standardized_cancer_type']
            if 'standardized_cancer_subtype' in standardized:
                model_data['standardized_cancer_subtype'] = standardized['standardized_cancer_subtype']

    def process_directory(self, input_dir: Path, output_dir: Path):
        """
        Process all extraction JSON files in a directory.

        Args:
            input_dir: Directory containing extraction JSON files
            output_dir: Directory to save standardized results
        """
        input_files = list(input_dir.glob('*_results.json'))

        if not input_files:
            logger.warning(f"No extraction result files found in {input_dir}")
            return

        logger.info(f"Found {len(input_files)} extraction files to process")

        for input_file in tqdm(input_files, desc="Standardizing cancer terms"):
            output_file = output_dir / input_file.name
            try:
                self.process_extraction_file(input_file, output_file)
            except Exception as e:
                logger.error(f"Error processing {input_file.name}: {e}")
                continue

        logger.info(f"Standardization complete. Results saved to {output_dir}")


def main():
    """Main function for command-line usage."""
    parser = argparse.ArgumentParser(description="Standardize cancer terms in extraction results")
    parser.add_argument("--input-dir", type=str,
                       help="Directory containing extraction result JSON files")
    parser.add_argument("--output-dir", type=str,
                       help="Directory to save standardized results")
    parser.add_argument("--input-file", type=str,
                       help="Single file to process (alternative to --input-dir)")
    parser.add_argument("--output-file", type=str,
                       help="Output file path (used with --input-file)")

    args = parser.parse_args()

    # Initialize standardizer
    standardizer = CancerTermStandardizer()

    if args.input_file:
        # Process single file
        if not args.output_file:
            raise ValueError("--output-file is required when using --input-file")

        input_file = Path(args.input_file)
        output_file = Path(args.output_file)
        standardizer.process_extraction_file(input_file, output_file)
    elif args.input_dir and args.output_dir:
        # Process directory
        input_dir = Path(args.input_dir)
        output_dir = Path(args.output_dir)
        standardizer.process_directory(input_dir, output_dir)
    else:
        parser.error("Either --input-file and --output-file, or --input-dir and --output-dir must be provided")


if __name__ == "__main__":
    main()